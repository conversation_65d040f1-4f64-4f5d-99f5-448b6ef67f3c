<?php
const RECAPTCHA_SITEKEY = '6LdTj5orAAAAAHOMrI749nPm9ksW-6kVNu0b4xe8';
?><!DOCTYPE html>
<html lang="cs">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>.pkt → JSON</title>
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/notyf@3/notyf.min.css">
  <script src="https://cdn.jsdelivr.net/npm/notyf@3/notyf.min.js"></script>
  <script src="https://www.google.com/recaptcha/api.js" async defer></script>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    :root {
      --bg-primary: #0a0b0f;
      --bg-secondary: #1a1d29;
      --bg-tertiary: #252938;
      --accent-primary: #6366f1;
      --accent-secondary: #8b5cf6;
      --text-primary: #f8fafc;
      --text-secondary: #cbd5e1;
      --text-muted: #64748b;
      --border-color: #334155;
      --success-color: #10b981;
      --error-color: #ef4444;
      --shadow-lg: 0 25px 50px -12px rgba(0, 0, 0, 0.8);
      --shadow-xl: 0 35px 60px -12px rgba(0, 0, 0, 0.9);
    }

    body {
      background: linear-gradient(135deg, var(--bg-primary) 0%, #0f1419 50%, var(--bg-primary) 100%);
      color: var(--text-primary);
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      min-height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 1rem;
      position: relative;
      overflow-x: hidden;
    }

    body::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
                  radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
      pointer-events: none;
      z-index: -1;
    }

    .wrapper {
      background: linear-gradient(145deg, var(--bg-secondary), #1e2235);
      padding: 3rem;
      border-radius: 24px;
      box-shadow: var(--shadow-xl);
      width: 100%;
      max-width: 520px;
      border: 1px solid rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(20px);
      position: relative;
      overflow: hidden;
    }

    .wrapper::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    }

    h2 {
      text-align: center;
      margin-bottom: 2rem;
      font-size: 2rem;
      font-weight: 700;
      background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      letter-spacing: -0.025em;
    }

    .subtitle {
      text-align: center;
      color: var(--text-muted);
      margin-bottom: 2.5rem;
      font-size: 0.95rem;
      font-weight: 400;
    }

    .version {
      position: absolute;
      top: 1rem;
      right: 1rem;
      background: rgba(255, 255, 255, 0.1);
      color: var(--text-muted);
      padding: 0.25rem 0.75rem;
      border-radius: 20px;
      font-size: 0.75rem;
      font-weight: 500;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    label {
      display: block;
      margin-bottom: 0.75rem;
      font-weight: 500;
      color: var(--text-secondary);
      font-size: 0.95rem;
    }

    .file-input-wrapper {
      position: relative;
      margin-bottom: 1.5rem;
    }

    .upload-zone {
      width: 100%;
      min-height: 120px;
      background: var(--bg-tertiary);
      border: 2px dashed var(--border-color);
      border-radius: 16px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .upload-zone:hover {
      border-color: var(--accent-primary);
      background: rgba(99, 102, 241, 0.05);
      transform: translateY(-2px);
    }

    .upload-zone.dragover {
      border-color: var(--accent-primary);
      background: rgba(99, 102, 241, 0.1);
      border-style: solid;
    }

    .upload-zone.has-file {
      border-color: var(--success-color);
      background: rgba(16, 185, 129, 0.05);
    }

    .upload-icon {
      font-size: 2rem;
      color: var(--text-muted);
      margin-bottom: 0.5rem;
      transition: all 0.3s ease;
    }

    .upload-zone:hover .upload-icon {
      color: var(--accent-primary);
      transform: scale(1.1);
    }

    .upload-text {
      color: var(--text-secondary);
      font-size: 0.95rem;
      font-weight: 500;
      text-align: center;
      margin-bottom: 0.25rem;
    }

    .upload-subtext {
      color: var(--text-muted);
      font-size: 0.8rem;
      text-align: center;
    }

    input[type="file"] {
      position: absolute;
      opacity: 0;
      width: 100%;
      height: 100%;
      cursor: pointer;
    }

    .file-preview {
      margin-top: 1rem;
      padding: 1rem;
      background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(16, 185, 129, 0.05));
      border: 1px solid rgba(16, 185, 129, 0.2);
      border-radius: 12px;
      display: none;
      position: relative;
      overflow: hidden;
    }

    .file-preview::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, transparent, rgba(16, 185, 129, 0.3), transparent);
    }

    .file-preview.error {
      background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.05));
      border-color: rgba(239, 68, 68, 0.2);
    }

    .file-preview.error::before {
      background: linear-gradient(90deg, transparent, rgba(239, 68, 68, 0.3), transparent);
    }

    .file-details {
      display: flex;
      align-items: center;
      gap: 0.75rem;
    }

    .file-icon {
      width: 2.5rem;
      height: 2.5rem;
      background: rgba(16, 185, 129, 0.2);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.25rem;
      color: var(--success-color);
      flex-shrink: 0;
    }

    .file-preview.error .file-icon {
      background: rgba(239, 68, 68, 0.2);
      color: var(--error-color);
    }

    .file-info-content {
      flex: 1;
      min-width: 0;
    }

    .file-name {
      font-weight: 600;
      color: var(--text-primary);
      font-size: 0.9rem;
      margin-bottom: 0.25rem;
      word-break: break-all;
    }

    .file-meta {
      color: var(--text-muted);
      font-size: 0.8rem;
      margin-bottom: 0.5rem;
    }

    .file-status {
      font-size: 0.85rem;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .file-status.success {
      color: var(--success-color);
    }

    .file-status.error {
      color: var(--error-color);
    }

    .status-icon {
      font-size: 1rem;
    }

    button {
      width: 100%;
      padding: 1rem 1.5rem;
      background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
      color: white;
      border: none;
      border-radius: 16px;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
    }

    button:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 20px 40px -12px rgba(99, 102, 241, 0.4);
    }

    button:active {
      transform: translateY(0);
    }

    button:disabled {
      opacity: 0.7;
      cursor: not-allowed;
      transform: none;
    }

    .spinner {
      width: 1.25rem;
      height: 1.25rem;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-top: 2px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      display: none;
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }

    @media (max-width: 640px) {
      .wrapper {
        padding: 2rem;
        margin: 1rem;
      }

      h2 {
        font-size: 1.75rem;
      }
    }

    .notyf__toast {
      border-radius: 12px !important;
      backdrop-filter: blur(20px) !important;
      border: 1px solid rgba(255, 255, 255, 0.1) !important;
      box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.8) !important;
    }

    .notyf__toast--success {
      background: linear-gradient(135deg, rgba(16, 185, 129, 0.9), rgba(5, 150, 105, 0.9)) !important;
      color: white !important;
    }

    .notyf__toast--error {
      background: linear-gradient(135deg, rgba(239, 68, 68, 0.9), rgba(220, 38, 38, 0.9)) !important;
      color: white !important;
    }

    .notyf__message {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif !important;
      font-weight: 500 !important;
      font-size: 0.9rem !important;
    }

    .notyf__icon {
      display: none !important;
    }

    .notyf__dismiss-btn {
      display: none !important;
    }

    .notyf__wrapper {
      padding-left: 16px !important;
    }
  </style>
</head>
<body>
  <div class="wrapper">
    <div class="version">v1.1</div>
    <h2>PKT→ JSON</h2>
    <p class="subtitle">Převeďte Vaši Packet Tracer topologii na základní lehce čitelný textový formát JSON, např. pro rychlou a přesnou práci s AI.</p>

    <form id="uploadForm">
      <label for="pktFile">Vyber .pkt nebo .pka soubor (max 2MB):</label>
      <div class="file-input-wrapper">
        <div class="upload-zone" id="uploadZone">
          <input type="file" id="pktFile" name="inputfile" accept=".pkt,.pka" required>
          <div class="upload-icon">📁</div>
          <div class="upload-text">Klikněte nebo přetáhněte soubor</div>
          <div class="upload-subtext">Podporované formáty: .pkt, .pka (max 2MB)</div>
        </div>
        <div class="file-preview" id="filePreview">
          <div class="file-details">
            <div class="file-icon" id="fileIcon">📄</div>
            <div class="file-info-content">
              <div class="file-name" id="fileName"></div>
              <div class="file-meta" id="fileMeta"></div>
              <div class="file-status" id="fileStatus"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="g-recaptcha" data-sitekey="<?= RECAPTCHA_SITEKEY ?>" data-theme="dark" style="margin-bottom: 1.5rem; display: flex; justify-content: center;"></div>
      <button type="submit" id="convertBtn">
        <span id="btnText">Konvertovat</span>
        <span class="spinner" id="loader"></span>
      </button>
    </form>
  </div>

  <script>
    const notyf = new Notyf({
      duration: 4000,
      position: {
        x: 'right',
        y: 'top'
      },
      dismissible: false,
      ripple: true,
      types: [
        {
          type: 'success',
          background: 'linear-gradient(135deg, rgba(16, 185, 129, 0.9), rgba(5, 150, 105, 0.9))',
          duration: 4000,
          dismissible: false,
          icon: false
        },
        {
          type: 'error',
          background: 'linear-gradient(135deg, rgba(239, 68, 68, 0.9), rgba(220, 38, 38, 0.9))',
          duration: 4000,
          dismissible: false,
          icon: false
        }
      ]
    });

    function showToast(type, message) {
      if (type === 'success') {
        notyf.success(message);
      } else if (type === 'error') {
        notyf.error(message);
      } else {
        notyf.open({
          type: 'info',
          message: message
        });
      }
    }

    function handleConversionError(error) {
      showToast('error', error || 'Konverze selhala');
    }

    const form = document.getElementById("uploadForm");
    const fileInput = document.getElementById("pktFile");
    const loader = document.getElementById("loader");
    const btnText = document.getElementById("btnText");
    const convertBtn = document.getElementById("convertBtn");
    const uploadZone = document.getElementById("uploadZone");
    const filePreview = document.getElementById("filePreview");
    const fileName = document.getElementById("fileName");
    const fileMeta = document.getElementById("fileMeta");
    const fileStatus = document.getElementById("fileStatus");
    const fileIcon = document.getElementById("fileIcon");

    function validateFile(file) {
      const validExtensions = ['.pkt', '.pka'];
      const maxSize = 2 * 1024 * 1024;

      if (!file) return { valid: false, error: 'Žádný soubor nebyl vybrán' };

      const extension = '.' + file.name.split('.').pop().toLowerCase();
      if (!validExtensions.includes(extension)) {
        return { valid: false, error: 'Neplatný typ souboru (pouze .pkt a .pka soubory)' };
      }

      if (file.size > maxSize) {
        return { valid: false, error: 'Soubor je příliš velký (max 2MB)' };
      }

      if (file.size === 0) {
        return { valid: false, error: 'Soubor je prázdný' };
      }

      return { valid: true };
    }

    function enforceFileTypeRestriction() {
      fileInput.setAttribute('accept', '.pkt,.pka');

      fileInput.addEventListener('change', function(e) {
        const files = Array.from(e.target.files);
        const invalidFiles = files.filter(file => {
          const extension = '.' + file.name.split('.').pop().toLowerCase();
          return !['.pkt', '.pka'].includes(extension);
        });

        if (invalidFiles.length > 0) {
          fileInput.value = '';
          showToast('error', 'Byly vybrány neplatné soubory. Pouze .pkt a .pka soubory jsou povoleny.');
          updateFilePreview(null);
          return false;
        }

        updateFilePreview(files[0]);
        return true;
      });
    }

    function updateFilePreview(file) {
      if (!file) {
        filePreview.style.display = 'none';
        uploadZone.classList.remove('has-file');
        return;
      }

      const validation = validateFile(file);
      const fileSize = (file.size / 1024 / 1024).toFixed(2);
      const extension = '.' + file.name.split('.').pop().toLowerCase();

      fileName.textContent = file.name;
      fileMeta.textContent = `Velikost: ${fileSize} MB • Typ: ${extension.toUpperCase()}`;

      if (validation.valid) {
        filePreview.className = 'file-preview';
        fileIcon.textContent = extension === '.pkt' ? '📦' : '📋';
        fileStatus.className = 'file-status success';
        fileStatus.innerHTML = '<span class="status-icon">✓</span>Připraven k konverzi';
        uploadZone.classList.add('has-file');
      } else {
        filePreview.className = 'file-preview error';
        fileIcon.textContent = '⚠️';
        fileStatus.className = 'file-status error';
        fileStatus.innerHTML = `<span class="status-icon">✗</span>${validation.error}`;
        uploadZone.classList.remove('has-file');
      }

      filePreview.style.display = 'block';
    }

    uploadZone.addEventListener('dragover', function(e) {
      e.preventDefault();
      e.stopPropagation();
      uploadZone.classList.add('dragover');
    });

    uploadZone.addEventListener('dragleave', function(e) {
      e.preventDefault();
      e.stopPropagation();
      uploadZone.classList.remove('dragover');
    });

    uploadZone.addEventListener('drop', function(e) {
      e.preventDefault();
      e.stopPropagation();
      uploadZone.classList.remove('dragover');

      const files = Array.from(e.dataTransfer.files);

      if (files.length === 0) {
        showToast('error', 'Žádné soubory nebyly přetaženy');
        return;
      }

      if (files.length > 1) {
        showToast('error', 'Prosím přetáhněte pouze jeden soubor');
        return;
      }

      const file = files[0];
      const validation = validateFile(file);

      if (!validation.valid) {
        showToast('error', validation.error);
        updateFilePreview(null);
        return;
      }

      try {
        const dt = new DataTransfer();
        dt.items.add(file);
        fileInput.files = dt.files;
        updateFilePreview(file);
      } catch (error) {
        console.error('Error setting file input:', error);
        updateFilePreview(file);
      }
    });

    form.addEventListener("submit", function (e) {
      e.preventDefault();

      const file = fileInput.files[0];

      const validation = validateFile(file);
      if (!validation.valid) {
        showToast('error', validation.error);
        return;
      }

      const currentAccept = fileInput.getAttribute('accept');
      if (currentAccept !== '.pkt,.pka') {
        console.warn('File input accept attribute was tampered with, resetting...');
        fileInput.setAttribute('accept', '.pkt,.pka');
        showToast('error', 'Bezpečnostní chyba: Prosím obnovte stránku a zkuste znovu');
        return;
      }

      const finalExtension = '.' + file.name.split('.').pop().toLowerCase();
      if (!['.pkt', '.pka'].includes(finalExtension)) {
        showToast('error', 'Bezpečnostní chyba: Neplatný typ souboru detekován při odesílání');
        return;
      }

      // Check if reCAPTCHA is completed
      const recaptchaResponse = grecaptcha.getResponse();
      if (!recaptchaResponse) {
        showToast('error', 'Prosím dokončete reCAPTCHA ověření.');
        return;
      }

      setLoadingState(true);

      const formData = new FormData();
      formData.append("inputfile", file);
      formData.append("g-recaptcha-response", recaptchaResponse);

      fetch("ajax.php", {
        method: "POST",
        body: formData,
      })
      .then(async response => {
        const json = await response.json();
        setLoadingState(false);

        if (json.success) {
          const blob = new Blob([JSON.stringify(json.data, null, 2)], {
            type: "application/json"
          });
          const link = document.createElement("a");
          link.href = URL.createObjectURL(blob);
          link.download = json.filename || "converted.json";
          link.click();

          const metadata = json.data.metadata;
          let successMsg = "Konverze úspěšná!";
          if (metadata) {
            successMsg += ` (${metadata.deviceCount} zařízení, ${metadata.linkCount} spojení)`;
          }
          showToast('success', successMsg);

          form.reset();
          filePreview.style.display = "none";
          uploadZone.classList.remove('has-file');
          // Reset reCAPTCHA after successful submission
          grecaptcha.reset();
        } else {
          handleConversionError(json.error);
          // Reset reCAPTCHA on error
          grecaptcha.reset();
        }
      })
      .catch(error => {
        setLoadingState(false);
        console.error("Conversion error:", error);
        showToast('error', "Chyba serveru nebo připojení");
        // Reset reCAPTCHA on error
        grecaptcha.reset();
      });
    });

    function setLoadingState(loading) {
      if (loading) {
        loader.style.display = "inline-block";
        btnText.textContent = "Konvertuji...";
        convertBtn.disabled = true;
      } else {
        loader.style.display = "none";
        btnText.textContent = "Konvertovat";
        convertBtn.disabled = false;
      }
    }

    enforceFileTypeRestriction();

    setInterval(function() {
      const currentAccept = fileInput.getAttribute('accept');
      if (currentAccept !== '.pkt,.pka') {
        console.warn('File input accept attribute was tampered with, resetting...');
        fileInput.setAttribute('accept', '.pkt,.pka');
      }
    }, 5000);
  </script>
</body>
</html>