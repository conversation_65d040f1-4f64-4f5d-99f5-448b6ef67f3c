<?php
const PKT_MAX_BYTES     = 2 * 1024 * 1024;
const RECAPTCHA_SECRET  = '6LdTj5orAAAAALSC0WbrWgM1Kl1K5HSRhGThnIsU';
const RECAPTCHA_MIN_SCORE = 0.3;

header('Content-Type: application/json; charset=utf-8');

function sendJsonResponse($success, $data = null, $error = null, $filename = null, $httpCode = 200) {
    http_response_code($httpCode);
    $response = [
        'success' => $success,
        'data' => $data,
        'error' => $error,
        'filename' => $filename
    ];
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    exit;
}

function cleanupTempFile($filePath) {
    if ($filePath && file_exists($filePath)) {
        @unlink($filePath);
    }
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendJsonResponse(false, null, 'Method Not Allowed', null, 405);
}

$token = $_POST['token'] ?? '';
if (empty($token)) {
    sendJsonResponse(false, null, 'reCAPTCHA token chybí', null, 400);
}

$recaptchaUrl = "https://www.google.com/recaptcha/api/siteverify?secret=" . RECAPTCHA_SECRET . "&response=" . $token;
$recaptchaResponse = @file_get_contents($recaptchaUrl);

if ($recaptchaResponse === false) {
    sendJsonResponse(false, null, 'Chyba při ověřování reCAPTCHA', null, 500);
}

$res = json_decode($recaptchaResponse, true);
if (empty($res['success']) || $res['score'] < RECAPTCHA_MIN_SCORE) {
    sendJsonResponse(false, null, 'reCAPTCHA ověření selhalo', null, 403);
}

if (empty($_FILES['inputfile'])) {
    sendJsonResponse(false, null, 'Žádný soubor nebyl nahrán', null, 400);
}

$file = $_FILES['inputfile'];
if ($file['error'] !== UPLOAD_ERR_OK) {
    $errorMessages = [
        UPLOAD_ERR_INI_SIZE => 'Soubor je příliš velký (server limit)',
        UPLOAD_ERR_FORM_SIZE => 'Soubor je příliš velký (form limit)',
        UPLOAD_ERR_PARTIAL => 'Soubor byl nahrán pouze částečně',
        UPLOAD_ERR_NO_FILE => 'Žádný soubor nebyl nahrán',
        UPLOAD_ERR_NO_TMP_DIR => 'Chybí dočasná složka',
        UPLOAD_ERR_CANT_WRITE => 'Nepodařilo se zapsat soubor na disk',
        UPLOAD_ERR_EXTENSION => 'Nahrávání souboru bylo zastaveno rozšířením'
    ];
    $errorMsg = $errorMessages[$file['error']] ?? 'Neznámá chyba při nahrávání souboru';
    sendJsonResponse(false, null, $errorMsg, null, 400);
}

if ($file['size'] > PKT_MAX_BYTES) {
    sendJsonResponse(false, null, 'Soubor je příliš velký (max 2MB)', null, 400);
}

$fileName = strtolower($file['name']);
if (!str_ends_with($fileName, '.pkt') && !str_ends_with($fileName, '.pka')) {
    sendJsonResponse(false, null, 'Neplatný typ souboru (pouze .pkt a .pka soubory)', null, 400);
}

if ($file['size'] === 0) {
    sendJsonResponse(false, null, 'Soubor je prázdný', null, 400);
}

$pkt = $file['tmp_name'];

if (!file_exists($pkt) || !is_readable($pkt)) {
    sendJsonResponse(false, null, 'Nahraný soubor není dostupný pro čtení', null, 500);
}

$xmlPath = tempnam(sys_get_temp_dir(), 'pkt_xml_');
if ($xmlPath === false) {
    sendJsonResponse(false, null, 'Nelze vytvořit dočasný soubor pro konverzi', null, 500);
}

$bin = __DIR__ . '/binary/pka2xml';

if (!file_exists($bin)) {
    cleanupTempFile($xmlPath);
    sendJsonResponse(false, null, 'Konverzní nástroj není dostupný', null, 500);
}

if (!is_executable($bin)) {
    cleanupTempFile($xmlPath);
    sendJsonResponse(false, null, 'Konverzní nástroj nemá oprávnění ke spuštění', null, 500);
}

$command = escapeshellcmd($bin) . " -d " . escapeshellarg($pkt) . " " . escapeshellarg($xmlPath);
$output = [];
$returnCode = 0;
exec($command, $output, $returnCode);

if ($returnCode !== 0) {
    cleanupTempFile($xmlPath);
    sendJsonResponse(false, null, 'Soubor je poškozen.', null, 500);
}

// Validate output file
if (!file_exists($xmlPath) || filesize($xmlPath) === 0) {
    cleanupTempFile($xmlPath);
    sendJsonResponse(false, null, 'Soubor je poškozen.', null, 500);
}

libxml_use_internal_errors(true);
$doc = simplexml_load_file($xmlPath);

if ($doc === false) {
    $xmlErrors = libxml_get_errors();
    $errorMsg = 'Chyba při parsování XML';
    if (!empty($xmlErrors)) {
        $errorMsg .= ': ' . $xmlErrors[0]->message;
    }
    cleanupTempFile($xmlPath);
    sendJsonResponse(false, null, $errorMsg, null, 500);
}

try {
    $dom = dom_import_simplexml($doc)->ownerDocument;
    $xp = new DOMXPath($dom);
    $ref2name = [];
    $devices = [];
    $links = [];
    $notes = [];
} catch (Exception $e) {
    cleanupTempFile($xmlPath);
    sendJsonResponse(false, null, 'Chyba při zpracování XML: ' . $e->getMessage(), null, 500);
}

foreach($xp->query('//*[local-name()="DEVICE"]') as $dev){
  $e = $xp->query('.//*[local-name()="ENGINE"]', $dev)->item(0); if (!$e) continue;
  $name = trim($xp->evaluate('string(./*[local-name()="NAME"])', $e)) ?: 'Unknown';
  $type = trim($xp->evaluate('string(./*[local-name()="TYPE"])', $e)) ?: 'Unknown';
  $id = trim($xp->evaluate('string(./*[local-name()="SAVE_REF_ID"])', $e)) ?: uniqid();
  $ref2name[$id] = $name;
  $note = trim($xp->evaluate('string(./*[local-name()="DESCRIPTION"])', $e));

  $ports = [];
  foreach($xp->query('.//*[local-name()="PORT"]', $dev) as $i => $p){
    $pn = trim($xp->evaluate('string(./*[local-name()="PORTID"]|./*[local-name()="NAME"])', $p)) ?: "Port_$i";
    $ports[] = [
      'Name' => $pn,
      'Type' => trim($xp->evaluate('string(./*[local-name()="TYPE"])', $p)),
      'Mac'  => trim($xp->evaluate('string(./*[local-name()="MACADDRESS"])', $p)),
      'Description' => trim($xp->evaluate('string(./*[local-name()="DESCRIPTION"])', $p))
    ];
  }
  $cfg = [];
  foreach($xp->query('.//*[local-name()="RUNNINGCONFIG"]/*[local-name()="LINE"]', $dev) as $l){
    $v = trim($l->textContent); if ($v !== '') $cfg[] = $v;
  }
  $devices[] = [
    'Name'=>$name,'Type'=>$type,'Notes'=>$note,'SaveRefId'=>$id,'Ports'=>$ports,'Config'=>$cfg
  ];
}

foreach($xp->query('//*[local-name()="LINK"]') as $lnk){
  $c = $xp->query('.//*[local-name()="CABLE"]', $lnk)->item(0); if (!$c) continue;
  $fromId = trim($xp->evaluate('string(./*[local-name()="FROM"])', $c));
  $fromPrt = trim($xp->evaluate('string(./*[local-name()="PORT"][1])', $c));
  $toId = trim($xp->evaluate('string(./*[local-name()="TO"])', $c));
  $toPrt = trim($xp->evaluate('string(./*[local-name()="PORT"][2])', $c));
  $links[] = [
    'FromDevice' => $ref2name[$fromId] ?? $fromId,
    'FromPort' => $fromPrt,
    'ToDevice' => $ref2name[$toId] ?? $toId,
    'ToPort' => $toPrt
  ];
}

foreach($xp->query('//*[local-name()="NOTE"]') as $n){
  $notes[] = [
    'Uuid' => $n->getAttribute('uuid') ?: '',
    'X' => (int)$xp->evaluate('string(./*[local-name()="X"])', $n),
    'Y' => (int)$xp->evaluate('string(./*[local-name()="Y"])', $n),
    'Z' => (int)$xp->evaluate('string(./*[local-name()="Z"])', $n),
    'Text' => trim($xp->evaluate('string(./*[local-name()="TEXT"])', $n))
  ];
}

try {
    $out = [
        'devices' => [],
        'links' => $links,
        'notes' => $notes,
        'metadata' => [
            'originalFilename' => $file['name'],
            'fileSize' => $file['size'],
            'convertedAt' => date('c'),
            'deviceCount' => count($devices),
            'linkCount' => count($links),
            'noteCount' => count($notes)
        ]
    ];

    foreach($devices as $d){
        $portMap = array_column($d['Ports'], null, 'Name');
        $out['devices'][$d['Name']] = [
            'type' => $d['Type'],
            'notes' => $d['Notes'],
            'config' => $d['Config'],
            'ports' => $portMap
        ];
    }

    cleanupTempFile($xmlPath);

    $outputFilename = pathinfo($file['name'], PATHINFO_FILENAME) . '_converted.json';

    sendJsonResponse(true, $out, null, $outputFilename);

} catch (Exception $e) {
    cleanupTempFile($xmlPath);
    sendJsonResponse(false, null, 'Chyba při sestavování výstupu: ' . $e->getMessage(), null, 500);
}